# TrackMyPrep - AI-Powered Study Planner

An intelligent study planner and productivity companion for students, featuring AI-powered session rebalancing, smart timers, and adaptive flashcards.

## 🚀 Features

### Core Features
- **User Authentication**: Email/password login with Firebase Auth + anonymous guest mode
- **Smart Study Planner**: Create and manage study sessions with calendar view
- **AI-Powered Session Rebalancer**: Automatically rebalance missed sessions using OpenAI
- **Pomodoro Timer**: Built-in timer with focus/break cycles
- **Flashcard System**: Create, review, and auto-generate flashcards
- **Daily Dashboard**: Overview of today's sessions and progress tracking
- **PWA Support**: Install as mobile app with offline capabilities

### Premium Features
- Unlimited AI rebalancing credits
- Advanced analytics and insights
- Unlimited flashcards and study sessions
- Priority support

## 🛠️ Tech Stack

- **Frontend**: Next.js 14 + TypeScript + Tailwind CSS
- **Authentication**: Firebase Auth
- **Database**: Firestore
- **AI**: OpenAI GPT-4 API
- **PWA**: next-pwa
- **Deployment**: Vercel
- **Payments**: Stripe (for premium features)

## 📁 Project Structure

```
src/
├── app/                    # Next.js app directory
│   ├── globals.css        # Global styles
│   ├── layout.tsx         # Root layout
│   └── page.tsx           # Home page
├── components/            # React components
│   ├── auth/             # Authentication components
│   ├── dashboard/        # Dashboard components
│   ├── layout/           # Layout components
│   ├── providers/        # Context providers
│   └── ui/               # Reusable UI components
├── lib/                  # Utilities and services
│   ├── auth/            # Authentication logic
│   ├── firebase/        # Firebase configuration
│   ├── hooks/           # Custom React hooks
│   ├── services/        # API services
│   ├── types/           # TypeScript types
│   └── utils.ts         # Utility functions
└── public/              # Static assets
```

## 🚀 Getting Started

### Prerequisites
- Node.js 18+ and npm/yarn
- Firebase project with Auth and Firestore enabled
- OpenAI API key
- Stripe account (for premium features)

### Installation

1. **Clone and install dependencies**:
```bash
git clone <repository-url>
cd trackmyprep
npm install
```

2. **Set up environment variables**:
```bash
cp .env.example .env.local
```

Fill in your Firebase, OpenAI, and Stripe credentials in `.env.local`.

3. **Run the development server**:
```bash
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) to view the app.

### Firebase Setup

1. Create a new Firebase project
2. Enable Authentication with Email/Password and Anonymous providers
3. Create a Firestore database
4. Add your Firebase config to `.env.local`

### OpenAI Setup

1. Get an API key from OpenAI
2. Add it to `.env.local` as `OPENAI_API_KEY`

## 📱 PWA Features

The app includes Progressive Web App capabilities:
- **Installable**: Add to home screen on mobile devices
- **Offline Support**: Core features work without internet
- **Responsive**: Optimized for mobile and desktop
- **Fast Loading**: Cached resources for quick startup

## 🎯 Usage

### For Students
1. **Sign up** or continue as guest
2. **Create study sessions** with subjects and topics
3. **Use the timer** for focused study periods
4. **Review flashcards** for active recall
5. **Track progress** on the dashboard

### AI Rebalancing
When you miss study sessions:
1. Click "AI Rebalance" on the dashboard
2. The AI analyzes your schedule and priorities
3. Get a new optimized schedule with one click
4. Continue studying with minimal disruption

## 🔧 Development

### Available Scripts
- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint

### Key Components
- **AuthProvider**: Manages user authentication state
- **AppLayout**: Main app layout with navigation
- **Dashboard**: Main dashboard with today's overview
- **TimerWidget**: Pomodoro timer component
- **useTimer**: Custom hook for timer functionality

## 🚀 Deployment

The app is configured for easy deployment on Vercel:

1. Connect your GitHub repository to Vercel
2. Add environment variables in Vercel dashboard
3. Deploy automatically on push to main branch

## 📄 License

This project is licensed under the MIT License.

## 🤝 Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## 📞 Support

For support, email <EMAIL> or create an issue in the repository.
