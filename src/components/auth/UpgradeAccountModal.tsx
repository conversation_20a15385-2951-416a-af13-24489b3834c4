'use client'

import { useState } from 'react'
import { useAuth } from '@/lib/auth/AuthProvider'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'
import { toast } from '@/components/ui/Toaster'
import { X, Crown, Check } from 'lucide-react'

interface UpgradeAccountModalProps {
  isOpen: boolean
  onClose: () => void
}

export function UpgradeAccountModal({ isOpen, onClose }: UpgradeAccountModalProps) {
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [loading, setLoading] = useState(false)
  const { upgradeGuestAccount } = useAuth()

  if (!isOpen) return null

  const handleUpgrade = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)

    try {
      await upgradeGuestAccount(email, password)
      toast('Account upgraded successfully!', 'success')
      onClose()
    } catch (error: any) {
      toast(error.message || 'Failed to upgrade account', 'error')
    } finally {
      setLoading(false)
    }
  }

  const features = [
    'Unlimited AI rebalancing credits',
    'Advanced progress analytics',
    'Unlimited flashcards and sessions',
    'Priority customer support',
    'Export study data',
    'Custom study themes'
  ]

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* Backdrop */}
      <div 
        className="absolute inset-0 bg-black bg-opacity-50" 
        onClick={onClose}
      />
      
      {/* Modal */}
      <div className="relative bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full mx-4 max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center space-x-2">
            <Crown className="h-6 w-6 text-yellow-500" />
            <h2 className="text-xl font-bold text-gray-900 dark:text-white">
              Upgrade to Premium
            </h2>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          {/* Features List */}
          <div className="mb-6">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
              Premium Features
            </h3>
            <ul className="space-y-3">
              {features.map((feature, index) => (
                <li key={index} className="flex items-center space-x-3">
                  <Check className="h-5 w-5 text-green-500 flex-shrink-0" />
                  <span className="text-gray-700 dark:text-gray-300">{feature}</span>
                </li>
              ))}
            </ul>
          </div>

          {/* Upgrade Form */}
          <form onSubmit={handleUpgrade} className="space-y-4">
            <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
              <p className="text-sm text-blue-800 dark:text-blue-200">
                Create an account to save your progress and unlock premium features.
                Your current session data will be preserved.
              </p>
            </div>

            <Input
              label="Email"
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              required
              placeholder="Enter your email"
            />
            
            <Input
              label="Password"
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              required
              placeholder="Create a password"
            />

            <div className="flex space-x-3 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={onClose}
                className="flex-1"
              >
                Maybe Later
              </Button>
              <Button
                type="submit"
                loading={loading}
                className="flex-1"
              >
                Upgrade Account
              </Button>
            </div>
          </form>

          {/* Pricing Info */}
          <div className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
            <div className="text-center">
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                $9.99<span className="text-sm font-normal text-gray-500">/month</span>
              </p>
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                Cancel anytime • 7-day free trial
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
