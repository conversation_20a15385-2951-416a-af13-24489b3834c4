'use client'

import { useState } from 'react'
import { useAuth } from '@/lib/auth/AuthProvider'
import { Button } from '@/components/ui/Button'
import { ThemeToggle } from '@/components/ui/ThemeToggle'
import { UpgradeAccountModal } from '@/components/auth/UpgradeAccountModal'
import {
  Brain,
  Calendar,
  Timer,
  BookOpen,
  Settings,
  LogOut,
  Menu,
  X,
  Crown
} from 'lucide-react'
import { cn } from '@/lib/utils'

interface AppLayoutProps {
  children: React.ReactNode
  currentPage?: 'dashboard' | 'planner' | 'timer' | 'flashcards' | 'settings'
}

export function AppLayout({ children, currentPage = 'dashboard' }: AppLayoutProps) {
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const [upgradeModalOpen, setUpgradeModalOpen] = useState(false)
  const { user, logout, isGuest } = useAuth()

  const navigation = [
    { name: 'Dashboard', href: '/', icon: Brain, key: 'dashboard' },
    { name: 'Study Planner', href: '/planner', icon: Calendar, key: 'planner' },
    { name: 'Timer', href: '/timer', icon: Timer, key: 'timer' },
    { name: 'Flashcards', href: '/flashcards', icon: BookOpen, key: 'flashcards' },
    { name: 'Settings', href: '/settings', icon: Settings, key: 'settings' },
  ]

  const handleLogout = async () => {
    try {
      await logout()
    } catch (error) {
      console.error('Logout failed:', error)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Mobile sidebar */}
      <div className={cn(
        'fixed inset-0 z-50 lg:hidden',
        sidebarOpen ? 'block' : 'hidden'
      )}>
        <div className="fixed inset-0 bg-gray-600 bg-opacity-75" onClick={() => setSidebarOpen(false)} />
        <div className="fixed inset-y-0 left-0 flex w-64 flex-col bg-white dark:bg-gray-800 shadow-xl">
          <div className="flex h-16 items-center justify-between px-4">
            <div className="flex items-center">
              <Brain className="h-8 w-8 text-primary-600" />
              <span className="ml-2 text-xl font-bold">TrackMyPrep</span>
            </div>
            <button onClick={() => setSidebarOpen(false)}>
              <X className="h-6 w-6" />
            </button>
          </div>
          <nav className="flex-1 px-4 py-4">
            <SidebarContent 
              navigation={navigation} 
              currentPage={currentPage}
              onItemClick={() => setSidebarOpen(false)}
            />
          </nav>
        </div>
      </div>

      {/* Desktop sidebar */}
      <div className="hidden lg:fixed lg:inset-y-0 lg:flex lg:w-64 lg:flex-col">
        <div className="flex flex-col flex-grow bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700">
          <div className="flex h-16 items-center px-4">
            <Brain className="h-8 w-8 text-primary-600" />
            <span className="ml-2 text-xl font-bold">TrackMyPrep</span>
          </div>
          <nav className="flex-1 px-4 py-4">
            <SidebarContent navigation={navigation} currentPage={currentPage} />
          </nav>
        </div>
      </div>

      {/* Main content */}
      <div className="lg:pl-64">
        {/* Top bar */}
        <div className="sticky top-0 z-40 flex h-16 items-center gap-x-4 border-b border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 px-4 shadow-sm sm:gap-x-6 sm:px-6 lg:px-8">
          <button
            type="button"
            className="-m-2.5 p-2.5 text-gray-700 dark:text-gray-200 lg:hidden"
            onClick={() => setSidebarOpen(true)}
          >
            <Menu className="h-6 w-6" />
          </button>

          <div className="flex flex-1 gap-x-4 self-stretch lg:gap-x-6">
            <div className="flex flex-1" />
            <div className="flex items-center gap-x-4 lg:gap-x-6">
              {isGuest && (
                <Button size="sm" className="flex items-center gap-2">
                  <Crown className="h-4 w-4" />
                  Upgrade Account
                </Button>
              )}
              <ThemeToggle />
              <Button
                variant="ghost"
                size="sm"
                onClick={handleLogout}
                className="flex items-center gap-2"
              >
                <LogOut className="h-4 w-4" />
                Sign Out
              </Button>
            </div>
          </div>
        </div>

        {/* Page content */}
        <main className="py-6">
          <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
            {children}
          </div>
        </main>
      </div>
    </div>
  )
}

function SidebarContent({ 
  navigation, 
  currentPage, 
  onItemClick 
}: { 
  navigation: any[]
  currentPage: string
  onItemClick?: () => void
}) {
  return (
    <ul className="space-y-1">
      {navigation.map((item) => (
        <li key={item.name}>
          <a
            href={item.href}
            onClick={onItemClick}
            className={cn(
              'group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold transition-colors',
              currentPage === item.key
                ? 'bg-primary-50 text-primary-600 dark:bg-primary-900/20 dark:text-primary-400'
                : 'text-gray-700 hover:text-primary-600 hover:bg-gray-50 dark:text-gray-300 dark:hover:text-primary-400 dark:hover:bg-gray-700'
            )}
          >
            <item.icon className="h-6 w-6 shrink-0" />
            {item.name}
          </a>
        </li>
      ))}
    </ul>
  )
}
