'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/Button'
import { Play, Pause, Square, RotateCcw } from 'lucide-react'
import { formatTime } from '@/lib/utils'

interface TimerState {
  timeLeft: number
  isRunning: boolean
  mode: 'focus' | 'break'
  session: number
}

export function TimerWidget() {
  const [timer, setTimer] = useState<TimerState>({
    timeLeft: 25 * 60, // 25 minutes in seconds
    isRunning: false,
    mode: 'focus',
    session: 1
  })

  useEffect(() => {
    let interval: NodeJS.Timeout | null = null

    if (timer.isRunning && timer.timeLeft > 0) {
      interval = setInterval(() => {
        setTimer(prev => ({
          ...prev,
          timeLeft: prev.timeLeft - 1
        }))
      }, 1000)
    } else if (timer.timeLeft === 0) {
      // Timer finished
      setTimer(prev => ({
        ...prev,
        isRunning: false,
        mode: prev.mode === 'focus' ? 'break' : 'focus',
        timeLeft: prev.mode === 'focus' ? 5 * 60 : 25 * 60, // 5 min break or 25 min focus
        session: prev.mode === 'focus' ? prev.session + 1 : prev.session
      }))
    }

    return () => {
      if (interval) clearInterval(interval)
    }
  }, [timer.isRunning, timer.timeLeft])

  const toggleTimer = () => {
    setTimer(prev => ({
      ...prev,
      isRunning: !prev.isRunning
    }))
  }

  const resetTimer = () => {
    setTimer({
      timeLeft: 25 * 60,
      isRunning: false,
      mode: 'focus',
      session: 1
    })
  }

  const stopTimer = () => {
    setTimer(prev => ({
      ...prev,
      isRunning: false
    }))
  }

  // Calculate progress for the circular progress bar
  const totalTime = timer.mode === 'focus' ? 25 * 60 : 5 * 60
  const progress = ((totalTime - timer.timeLeft) / totalTime) * 100
  const circumference = 2 * Math.PI * 45 // radius = 45
  const strokeDasharray = circumference
  const strokeDashoffset = circumference - (progress / 100) * circumference

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
      <div className="p-6 border-b border-gray-200 dark:border-gray-700">
        <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
          Pomodoro Timer
        </h2>
        <p className="text-sm text-gray-600 dark:text-gray-400">
          Session {timer.session} • {timer.mode === 'focus' ? 'Focus Time' : 'Break Time'}
        </p>
      </div>

      <div className="p-6">
        <div className="flex flex-col items-center space-y-6">
          {/* Circular Progress */}
          <div className="relative">
            <svg className="w-32 h-32 transform -rotate-90" viewBox="0 0 100 100">
              {/* Background circle */}
              <circle
                cx="50"
                cy="50"
                r="45"
                stroke="currentColor"
                strokeWidth="8"
                fill="none"
                className="text-gray-200 dark:text-gray-700"
              />
              {/* Progress circle */}
              <circle
                cx="50"
                cy="50"
                r="45"
                stroke="currentColor"
                strokeWidth="8"
                fill="none"
                strokeDasharray={strokeDasharray}
                strokeDashoffset={strokeDashoffset}
                className={`transition-all duration-1000 ${
                  timer.mode === 'focus' 
                    ? 'text-primary-600 dark:text-primary-400' 
                    : 'text-green-600 dark:text-green-400'
                }`}
                strokeLinecap="round"
              />
            </svg>
            
            {/* Time display */}
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-900 dark:text-white">
                  {formatTime(timer.timeLeft)}
                </div>
              </div>
            </div>
          </div>

          {/* Controls */}
          <div className="flex items-center space-x-3">
            <Button
              variant="outline"
              size="sm"
              onClick={resetTimer}
            >
              <RotateCcw className="h-4 w-4" />
            </Button>
            
            <Button
              onClick={toggleTimer}
              className="px-6"
            >
              {timer.isRunning ? (
                <Pause className="h-4 w-4 mr-2" />
              ) : (
                <Play className="h-4 w-4 mr-2" />
              )}
              {timer.isRunning ? 'Pause' : 'Start'}
            </Button>
            
            <Button
              variant="outline"
              size="sm"
              onClick={stopTimer}
            >
              <Square className="h-4 w-4" />
            </Button>
          </div>

          {/* Quick Settings */}
          <div className="w-full">
            <p className="text-sm font-medium text-gray-900 dark:text-white mb-2">
              Quick Start
            </p>
            <div className="grid grid-cols-2 gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setTimer({
                  timeLeft: 25 * 60,
                  isRunning: false,
                  mode: 'focus',
                  session: 1
                })}
              >
                25min Focus
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setTimer({
                  timeLeft: 50 * 60,
                  isRunning: false,
                  mode: 'focus',
                  session: 1
                })}
              >
                50min Deep Work
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
