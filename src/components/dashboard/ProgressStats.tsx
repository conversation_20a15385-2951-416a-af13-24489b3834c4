'use client'

import { 
  TrendingUp, 
  Clock, 
  Target, 
  Flame,
  BookOpen,
  CheckCircle
} from 'lucide-react'

interface StatCard {
  title: string
  value: string
  change: string
  changeType: 'positive' | 'negative' | 'neutral'
  icon: React.ComponentType<any>
}

export function ProgressStats() {
  // Mock data - will be replaced with Firebase data
  const stats: StatCard[] = [
    {
      title: 'Study Streak',
      value: '7 days',
      change: '+2 from last week',
      changeType: 'positive',
      icon: Flame
    },
    {
      title: 'Hours Today',
      value: '2.5h',
      change: '75% of daily goal',
      changeType: 'positive',
      icon: Clock
    },
    {
      title: 'Sessions Completed',
      value: '12',
      change: '+4 from yesterday',
      changeType: 'positive',
      icon: CheckCircle
    },
    {
      title: 'Flashcards Reviewed',
      value: '45',
      change: '90% accuracy',
      changeType: 'positive',
      icon: BookOpen
    },
    {
      title: 'Weekly Goal',
      value: '15h',
      change: '12h completed',
      changeType: 'positive',
      icon: Target
    },
    {
      title: 'Focus Score',
      value: '85%',
      change: '+5% this week',
      changeType: 'positive',
      icon: TrendingUp
    }
  ]

  const getChangeColor = (type: StatCard['changeType']) => {
    switch (type) {
      case 'positive':
        return 'text-green-600 dark:text-green-400'
      case 'negative':
        return 'text-red-600 dark:text-red-400'
      default:
        return 'text-gray-600 dark:text-gray-400'
    }
  }

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
      <div className="p-6 border-b border-gray-200 dark:border-gray-700">
        <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
          Progress Overview
        </h2>
        <p className="text-sm text-gray-600 dark:text-gray-400">
          Your study statistics and achievements
        </p>
      </div>

      <div className="p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {stats.map((stat, index) => (
            <div
              key={index}
              className="p-4 rounded-lg bg-gray-50 dark:bg-gray-700/50 border border-gray-100 dark:border-gray-600"
            >
              <div className="flex items-center justify-between mb-2">
                <stat.icon className="h-5 w-5 text-primary-600 dark:text-primary-400" />
                <span className="text-2xl font-bold text-gray-900 dark:text-white">
                  {stat.value}
                </span>
              </div>
              <h3 className="text-sm font-medium text-gray-900 dark:text-white mb-1">
                {stat.title}
              </h3>
              <p className={`text-xs ${getChangeColor(stat.changeType)}`}>
                {stat.change}
              </p>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}
