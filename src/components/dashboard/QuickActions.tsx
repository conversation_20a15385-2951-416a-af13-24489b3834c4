'use client'

import { But<PERSON> } from '@/components/ui/Button'
import { 
  Plus, 
  Play, 
  BookOpen, 
  Calendar, 
  Zap,
  Clock
} from 'lucide-react'

export function QuickActions() {
  const actions = [
    {
      title: 'Start Quick Session',
      description: '25-min Pomodoro',
      icon: Play,
      color: 'bg-green-500',
      action: () => console.log('Start quick session')
    },
    {
      title: 'Create Session',
      description: 'Plan new study time',
      icon: Plus,
      color: 'bg-blue-500',
      action: () => console.log('Create session')
    },
    {
      title: 'Review Flashcards',
      description: 'Continue learning',
      icon: BookOpen,
      color: 'bg-purple-500',
      action: () => console.log('Review flashcards')
    },
    {
      title: 'View Calendar',
      description: 'See full schedule',
      icon: Calendar,
      color: 'bg-orange-500',
      action: () => console.log('View calendar')
    },
    {
      title: 'AI Rebalance',
      description: 'Optimize schedule',
      icon: Zap,
      color: 'bg-yellow-500',
      action: () => console.log('AI rebalance')
    },
    {
      title: 'Deep Work',
      description: '90-min focus session',
      icon: Clock,
      color: 'bg-red-500',
      action: () => console.log('Deep work')
    }
  ]

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
      <div className="p-6 border-b border-gray-200 dark:border-gray-700">
        <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
          Quick Actions
        </h2>
        <p className="text-sm text-gray-600 dark:text-gray-400">
          Jump into your study routine
        </p>
      </div>

      <div className="p-6">
        <div className="grid grid-cols-2 gap-3">
          {actions.map((action, index) => (
            <button
              key={index}
              onClick={action.action}
              className="p-4 rounded-lg border border-gray-200 dark:border-gray-700 hover:border-primary-300 dark:hover:border-primary-600 transition-colors text-left group"
            >
              <div className="flex items-center space-x-3">
                <div className={`p-2 rounded-md ${action.color} text-white`}>
                  <action.icon className="h-4 w-4" />
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900 dark:text-white group-hover:text-primary-600 dark:group-hover:text-primary-400">
                    {action.title}
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    {action.description}
                  </p>
                </div>
              </div>
            </button>
          ))}
        </div>
      </div>
    </div>
  )
}
