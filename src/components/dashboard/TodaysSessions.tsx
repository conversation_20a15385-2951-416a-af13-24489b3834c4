'use client'

import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/Button'
import { Clock, Play, CheckCircle, AlertCircle } from 'lucide-react'
import { cn } from '@/lib/utils'

interface StudySession {
  id: string
  subject: string
  topic: string
  duration: number // in minutes
  startTime: string
  status: 'upcoming' | 'in-progress' | 'completed' | 'missed'
  type: 'pomodoro' | 'deep-work'
}

// Mock data - will be replaced with Firebase data
const mockSessions: StudySession[] = [
  {
    id: '1',
    subject: 'Mathematics',
    topic: 'Calculus - Derivatives',
    duration: 25,
    startTime: '09:00',
    status: 'completed',
    type: 'pomodoro'
  },
  {
    id: '2',
    subject: 'Physics',
    topic: 'Quantum Mechanics',
    duration: 50,
    startTime: '10:30',
    status: 'in-progress',
    type: 'deep-work'
  },
  {
    id: '3',
    subject: 'Chemistry',
    topic: 'Organic Chemistry',
    duration: 25,
    startTime: '14:00',
    status: 'upcoming',
    type: 'pomodoro'
  },
  {
    id: '4',
    subject: 'Biology',
    topic: 'Cell Biology',
    duration: 25,
    startTime: '15:30',
    status: 'upcoming',
    type: 'pomodoro'
  }
]

export function TodaysSessions() {
  const [sessions] = useState<StudySession[]>(mockSessions)

  const getStatusIcon = (status: StudySession['status']) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-5 w-5 text-green-500" />
      case 'in-progress':
        return <Play className="h-5 w-5 text-blue-500" />
      case 'missed':
        return <AlertCircle className="h-5 w-5 text-red-500" />
      default:
        return <Clock className="h-5 w-5 text-gray-400" />
    }
  }

  const getStatusColor = (status: StudySession['status']) => {
    switch (status) {
      case 'completed':
        return 'bg-green-50 border-green-200 dark:bg-green-900/20 dark:border-green-800'
      case 'in-progress':
        return 'bg-blue-50 border-blue-200 dark:bg-blue-900/20 dark:border-blue-800'
      case 'missed':
        return 'bg-red-50 border-red-200 dark:bg-red-900/20 dark:border-red-800'
      default:
        return 'bg-white border-gray-200 dark:bg-gray-800 dark:border-gray-700'
    }
  }

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
      <div className="p-6 border-b border-gray-200 dark:border-gray-700">
        <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
          Today's Study Sessions
        </h2>
        <p className="text-sm text-gray-600 dark:text-gray-400">
          {sessions.filter(s => s.status === 'completed').length} of {sessions.length} sessions completed
        </p>
      </div>

      <div className="p-6">
        <div className="space-y-4">
          {sessions.map((session) => (
            <div
              key={session.id}
              className={cn(
                'p-4 rounded-lg border transition-colors',
                getStatusColor(session.status)
              )}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  {getStatusIcon(session.status)}
                  <div>
                    <h3 className="font-medium text-gray-900 dark:text-white">
                      {session.subject}
                    </h3>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      {session.topic}
                    </p>
                  </div>
                </div>
                
                <div className="flex items-center space-x-4">
                  <div className="text-right">
                    <p className="text-sm font-medium text-gray-900 dark:text-white">
                      {session.startTime}
                    </p>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      {session.duration}min • {session.type}
                    </p>
                  </div>
                  
                  {session.status === 'upcoming' && (
                    <Button size="sm">
                      Start
                    </Button>
                  )}
                  
                  {session.status === 'in-progress' && (
                    <Button size="sm" variant="secondary">
                      Resume
                    </Button>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>

        {sessions.length === 0 && (
          <div className="text-center py-8">
            <Clock className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              No sessions scheduled
            </h3>
            <p className="text-gray-600 dark:text-gray-400 mb-4">
              Create your first study session to get started
            </p>
            <Button>
              Create Session
            </Button>
          </div>
        )}
      </div>
    </div>
  )
}
