'use client'

import { AppLayout } from '@/components/layout/AppLayout'
import { TodaysSessions } from './TodaysSessions'
import { QuickActions } from './QuickActions'
import { ProgressStats } from './ProgressStats'
import { TimerWidget } from './TimerWidget'

export function Dashboard() {
  return (
    <AppLayout currentPage="dashboard">
      <div className="space-y-6">
        {/* Welcome Section */}
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            Good morning! Ready to study?
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            Here's your study overview for today
          </p>
        </div>

        {/* Main Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Left Column - Today's Sessions */}
          <div className="lg:col-span-2 space-y-6">
            <TodaysSessions />
            <ProgressStats />
          </div>

          {/* Right Column - Timer & Quick Actions */}
          <div className="space-y-6">
            <TimerWidget />
            <QuickActions />
          </div>
        </div>
      </div>
    </AppLayout>
  )
}
