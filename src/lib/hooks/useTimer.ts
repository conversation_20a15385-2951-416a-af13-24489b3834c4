'use client'

import { useState, useEffect, useCallback } from 'react'

export interface TimerState {
  timeLeft: number
  totalTime: number
  isRunning: boolean
  isCompleted: boolean
  mode: 'focus' | 'break'
  session: number
}

export interface TimerConfig {
  focusDuration: number // in minutes
  breakDuration: number // in minutes
  longBreakDuration: number // in minutes
  sessionsUntilLongBreak: number
}

const DEFAULT_CONFIG: TimerConfig = {
  focusDuration: 25,
  breakDuration: 5,
  longBreakDuration: 15,
  sessionsUntilLongBreak: 4
}

export function useTimer(config: Partial<TimerConfig> = {}) {
  const timerConfig = { ...DEFAULT_CONFIG, ...config }
  
  const [timer, setTimer] = useState<TimerState>({
    timeLeft: timerConfig.focusDuration * 60,
    totalTime: timerConfig.focusDuration * 60,
    isRunning: false,
    isCompleted: false,
    mode: 'focus',
    session: 1
  })

  const [onComplete, setOnComplete] = useState<(() => void) | null>(null)

  useEffect(() => {
    let interval: NodeJS.Timeout | null = null

    if (timer.isRunning && timer.timeLeft > 0) {
      interval = setInterval(() => {
        setTimer(prev => ({
          ...prev,
          timeLeft: prev.timeLeft - 1
        }))
      }, 1000)
    } else if (timer.timeLeft === 0 && !timer.isCompleted) {
      // Timer completed
      setTimer(prev => ({
        ...prev,
        isRunning: false,
        isCompleted: true
      }))
      
      if (onComplete) {
        onComplete()
      }
    }

    return () => {
      if (interval) clearInterval(interval)
    }
  }, [timer.isRunning, timer.timeLeft, timer.isCompleted, onComplete])

  const start = useCallback(() => {
    setTimer(prev => ({
      ...prev,
      isRunning: true,
      isCompleted: false
    }))
  }, [])

  const pause = useCallback(() => {
    setTimer(prev => ({
      ...prev,
      isRunning: false
    }))
  }, [])

  const reset = useCallback(() => {
    const duration = timer.mode === 'focus' 
      ? timerConfig.focusDuration 
      : (timer.session % timerConfig.sessionsUntilLongBreak === 0 
          ? timerConfig.longBreakDuration 
          : timerConfig.breakDuration)
    
    setTimer(prev => ({
      ...prev,
      timeLeft: duration * 60,
      totalTime: duration * 60,
      isRunning: false,
      isCompleted: false
    }))
  }, [timer.mode, timer.session, timerConfig])

  const nextSession = useCallback(() => {
    const isLongBreak = timer.session % timerConfig.sessionsUntilLongBreak === 0
    const nextMode = timer.mode === 'focus' ? 'break' : 'focus'
    const nextSession = timer.mode === 'focus' ? timer.session : timer.session + 1
    
    let duration: number
    if (nextMode === 'focus') {
      duration = timerConfig.focusDuration
    } else {
      duration = isLongBreak ? timerConfig.longBreakDuration : timerConfig.breakDuration
    }

    setTimer({
      timeLeft: duration * 60,
      totalTime: duration * 60,
      isRunning: false,
      isCompleted: false,
      mode: nextMode,
      session: nextSession
    })
  }, [timer.mode, timer.session, timerConfig])

  const setCustomTime = useCallback((minutes: number, mode: 'focus' | 'break' = 'focus') => {
    setTimer({
      timeLeft: minutes * 60,
      totalTime: minutes * 60,
      isRunning: false,
      isCompleted: false,
      mode,
      session: 1
    })
  }, [])

  const progress = ((timer.totalTime - timer.timeLeft) / timer.totalTime) * 100

  return {
    timer,
    progress,
    start,
    pause,
    reset,
    nextSession,
    setCustomTime,
    setOnComplete
  }
}
