import { 
  collection, 
  doc, 
  addDoc, 
  updateDoc, 
  deleteDoc, 
  getDoc, 
  getDocs, 
  query, 
  where, 
  orderBy, 
  limit,
  Timestamp 
} from 'firebase/firestore'
import { db } from './config'
import { StudySession, Flashcard, FlashcardSet, StudyStats } from '@/lib/types'

// Study Sessions
export const studySessionsCollection = collection(db, 'studySessions')

export async function createStudySession(session: Omit<StudySession, 'id' | 'createdAt' | 'updatedAt'>) {
  const now = new Date()
  const docRef = await addDoc(studySessionsCollection, {
    ...session,
    scheduledStart: Timestamp.fromDate(session.scheduledStart),
    actualStart: session.actualStart ? Timestamp.fromDate(session.actualStart) : null,
    actualEnd: session.actualEnd ? Timestamp.fromDate(session.actualEnd) : null,
    createdAt: Timestamp.fromDate(now),
    updatedAt: Timestamp.fromDate(now)
  })
  return docRef.id
}

export async function updateStudySession(id: string, updates: Partial<StudySession>) {
  const docRef = doc(studySessionsCollection, id)
  const updateData = {
    ...updates,
    updatedAt: Timestamp.fromDate(new Date())
  }
  
  // Convert Date objects to Timestamps
  if (updates.scheduledStart) {
    updateData.scheduledStart = Timestamp.fromDate(updates.scheduledStart)
  }
  if (updates.actualStart) {
    updateData.actualStart = Timestamp.fromDate(updates.actualStart)
  }
  if (updates.actualEnd) {
    updateData.actualEnd = Timestamp.fromDate(updates.actualEnd)
  }
  
  await updateDoc(docRef, updateData)
}

export async function getUserStudySessions(userId: string, startDate?: Date, endDate?: Date) {
  let q = query(
    studySessionsCollection,
    where('userId', '==', userId),
    orderBy('scheduledStart', 'desc')
  )

  if (startDate && endDate) {
    q = query(
      studySessionsCollection,
      where('userId', '==', userId),
      where('scheduledStart', '>=', Timestamp.fromDate(startDate)),
      where('scheduledStart', '<=', Timestamp.fromDate(endDate)),
      orderBy('scheduledStart', 'desc')
    )
  }

  const snapshot = await getDocs(q)
  return snapshot.docs.map(doc => ({
    id: doc.id,
    ...doc.data(),
    scheduledStart: doc.data().scheduledStart?.toDate(),
    actualStart: doc.data().actualStart?.toDate(),
    actualEnd: doc.data().actualEnd?.toDate(),
    createdAt: doc.data().createdAt?.toDate(),
    updatedAt: doc.data().updatedAt?.toDate()
  })) as StudySession[]
}

// Flashcards
export const flashcardsCollection = collection(db, 'flashcards')
export const flashcardSetsCollection = collection(db, 'flashcardSets')

export async function createFlashcard(flashcard: Omit<Flashcard, 'id' | 'createdAt' | 'updatedAt'>) {
  const now = new Date()
  const docRef = await addDoc(flashcardsCollection, {
    ...flashcard,
    lastReviewed: flashcard.lastReviewed ? Timestamp.fromDate(flashcard.lastReviewed) : null,
    nextReview: flashcard.nextReview ? Timestamp.fromDate(flashcard.nextReview) : null,
    createdAt: Timestamp.fromDate(now),
    updatedAt: Timestamp.fromDate(now)
  })
  return docRef.id
}

export async function updateFlashcard(id: string, updates: Partial<Flashcard>) {
  const docRef = doc(flashcardsCollection, id)
  const updateData = {
    ...updates,
    updatedAt: Timestamp.fromDate(new Date())
  }
  
  if (updates.lastReviewed) {
    updateData.lastReviewed = Timestamp.fromDate(updates.lastReviewed)
  }
  if (updates.nextReview) {
    updateData.nextReview = Timestamp.fromDate(updates.nextReview)
  }
  
  await updateDoc(docRef, updateData)
}

export async function getUserFlashcards(userId: string, subject?: string) {
  let q = query(
    flashcardsCollection,
    where('userId', '==', userId),
    orderBy('createdAt', 'desc')
  )

  if (subject) {
    q = query(
      flashcardsCollection,
      where('userId', '==', userId),
      where('subject', '==', subject),
      orderBy('createdAt', 'desc')
    )
  }

  const snapshot = await getDocs(q)
  return snapshot.docs.map(doc => ({
    id: doc.id,
    ...doc.data(),
    lastReviewed: doc.data().lastReviewed?.toDate(),
    nextReview: doc.data().nextReview?.toDate(),
    createdAt: doc.data().createdAt?.toDate(),
    updatedAt: doc.data().updatedAt?.toDate()
  })) as Flashcard[]
}

// Study Stats
export const studyStatsCollection = collection(db, 'studyStats')

export async function saveStudyStats(stats: Omit<StudyStats, 'date'> & { date: Date }) {
  const docId = `${stats.userId}_${stats.date.toISOString().split('T')[0]}`
  const docRef = doc(studyStatsCollection, docId)
  
  await updateDoc(docRef, {
    ...stats,
    date: Timestamp.fromDate(stats.date)
  })
}

export async function getUserStudyStats(userId: string, startDate: Date, endDate: Date) {
  const q = query(
    studyStatsCollection,
    where('userId', '==', userId),
    where('date', '>=', Timestamp.fromDate(startDate)),
    where('date', '<=', Timestamp.fromDate(endDate)),
    orderBy('date', 'desc')
  )

  const snapshot = await getDocs(q)
  return snapshot.docs.map(doc => ({
    ...doc.data(),
    date: doc.data().date?.toDate()
  })) as StudyStats[]
}
