// User types
export interface User {
  id: string
  email?: string
  isAnonymous: boolean
  isPremium: boolean
  createdAt: Date
  preferences: UserPreferences
}

export interface UserPreferences {
  theme: 'light' | 'dark' | 'system'
  defaultFocusDuration: number // in minutes
  defaultBreakDuration: number // in minutes
  enableNotifications: boolean
  enableAISuggestions: boolean
  studyGoalHours: number // daily goal in hours
}

// Study Session types
export interface StudySession {
  id: string
  userId: string
  subject: string
  topic: string
  duration: number // in minutes
  scheduledStart: Date
  actualStart?: Date
  actualEnd?: Date
  status: 'scheduled' | 'in-progress' | 'completed' | 'missed' | 'cancelled'
  type: 'pomodoro' | 'deep-work' | 'custom'
  notes?: string
  createdAt: Date
  updatedAt: Date
}

// Flashcard types
export interface Flashcard {
  id: string
  userId: string
  subject: string
  front: string
  back: string
  difficulty: 'easy' | 'medium' | 'hard'
  lastReviewed?: Date
  nextReview?: Date
  reviewCount: number
  correctCount: number
  createdAt: Date
  updatedAt: Date
}

export interface FlashcardSet {
  id: string
  userId: string
  name: string
  description?: string
  subject: string
  flashcards: string[] // flashcard IDs
  createdAt: Date
  updatedAt: Date
}

// AI types
export interface AIRebalanceRequest {
  userId: string
  missedSessions: StudySession[]
  upcomingSessions: StudySession[]
  preferences: UserPreferences
  constraints: {
    availableTimeSlots: TimeSlot[]
    prioritySubjects: string[]
  }
}

export interface TimeSlot {
  start: Date
  end: Date
  available: boolean
}

export interface AIRebalanceResponse {
  success: boolean
  rebalancedSessions: StudySession[]
  reasoning: string
  confidence: number
}

// Progress tracking types
export interface StudyStats {
  userId: string
  date: Date
  totalStudyTime: number // in minutes
  sessionsCompleted: number
  sessionsMissed: number
  focusScore: number // 0-100
  subjects: {
    [subject: string]: {
      timeSpent: number
      sessionsCompleted: number
    }
  }
}

export interface Achievement {
  id: string
  name: string
  description: string
  icon: string
  unlockedAt?: Date
  progress: number // 0-100
  target: number
}
