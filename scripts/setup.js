#!/usr/bin/env node

const fs = require('fs')
const path = require('path')

console.log('🚀 Setting up TrackMyPrep...\n')

// Check if .env.local exists
const envPath = path.join(process.cwd(), '.env.local')
const envExamplePath = path.join(process.cwd(), '.env.example')

if (!fs.existsSync(envPath)) {
  if (fs.existsSync(envExamplePath)) {
    fs.copyFileSync(envExamplePath, envPath)
    console.log('✅ Created .env.local from .env.example')
    console.log('📝 Please fill in your Firebase, OpenAI, and Stripe credentials in .env.local\n')
  } else {
    console.log('❌ .env.example not found')
  }
} else {
  console.log('✅ .env.local already exists\n')
}

// Create placeholder icon files if they don't exist
const publicDir = path.join(process.cwd(), 'public')
const iconFiles = ['favicon.ico', 'icon-192x192.png', 'icon-512x512.png']

iconFiles.forEach(iconFile => {
  const iconPath = path.join(publicDir, iconFile)
  if (!fs.existsSync(iconPath)) {
    // Create a simple placeholder file
    fs.writeFileSync(iconPath, `# Placeholder for ${iconFile}\n# Replace with actual icon file`)
    console.log(`📁 Created placeholder for ${iconFile}`)
  }
})

console.log('\n🎯 Next steps:')
console.log('1. Fill in your credentials in .env.local')
console.log('2. Replace placeholder icon files in /public with actual icons')
console.log('3. Run "npm run dev" to start the development server')
console.log('4. Visit http://localhost:3000 to see your app')

console.log('\n📚 Documentation:')
console.log('- Firebase setup: https://firebase.google.com/docs/web/setup')
console.log('- OpenAI API: https://platform.openai.com/docs/api-reference')
console.log('- Stripe setup: https://stripe.com/docs/development')

console.log('\n✨ Happy coding!')
