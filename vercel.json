{"buildCommand": "npm run build", "outputDirectory": ".next", "framework": "nextjs", "installCommand": "npm install", "env": {"NEXT_PUBLIC_FIREBASE_API_KEY": "@firebase_api_key", "NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN": "@firebase_auth_domain", "NEXT_PUBLIC_FIREBASE_PROJECT_ID": "@firebase_project_id", "NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET": "@firebase_storage_bucket", "NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID": "@firebase_messaging_sender_id", "NEXT_PUBLIC_FIREBASE_APP_ID": "@firebase_app_id", "OPENAI_API_KEY": "@openai_api_key", "NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY": "@stripe_publishable_key", "STRIPE_SECRET_KEY": "@stripe_secret_key", "STRIPE_WEBHOOK_SECRET": "@stripe_webhook_secret"}, "headers": [{"source": "/sw.js", "headers": [{"key": "Cache-Control", "value": "public, max-age=0, must-revalidate"}]}]}